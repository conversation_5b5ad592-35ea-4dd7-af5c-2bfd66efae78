# FaceTrack Attendance System - Setup Guide

## Firebase Setup

### Step 1: Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Name your project: `facetrack-attendance`
4. Enable Google Analytics (optional)
5. Create project

### Step 2: Add Android App
1. Click "Add app" and select Android
2. Package name: `com.example.facetrack`
3. App nickname: `FaceTrack`
4. Download `google-services.json`
5. Replace the placeholder `app/google-services.json` with the downloaded file

### Step 3: Enable Authentication
1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Click "Save"

### Step 4: Setup Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (for development)
4. Select a location (choose closest to your region)
5. Click "Done"

### Step 5: Setup Storage (for face data)
1. Go to "Storage"
2. Click "Get started"
3. Choose "Start in test mode"
4. Select same location as Firestore
5. Click "Done"

## Database Collections Structure

The app will automatically create these collections:

### Students Collection
```
students/
  {studentNumber}/
    - studentNumber: string (8 digits)
    - name: string
    - surname: string
    - email: string (format: <EMAIL>)
    - password: string (format: $$Dut + first 6 digits of ID)
    - faculty: string
    - course: string
    - faceDataWithoutGlasses: string (base64 encoded)
    - faceDataWithGlasses: string (base64 encoded, optional)
    - hasGlasses: boolean
    - isActive: boolean
    - createdAt: timestamp
    - updatedAt: timestamp
```

### Staff Collection
```
staff/
  {staffId}/
    - staffId: string
    - name: string
    - surname: string
    - email: string (format: <EMAIL>)
    - password: string (custom password)
    - department: string
    - faculty: string
    - isActive: boolean
    - createdAt: timestamp
    - updatedAt: timestamp
```

### Attendance Collection
```
attendance/
  {attendanceId}/
    - attendanceId: string (auto-generated)
    - studentNumber: string
    - studentName: string
    - studentSurname: string
    - courseCode: string
    - courseName: string
    - lecturerStaffId: string
    - lecturerName: string
    - sessionType: string (LECTURE, TUTORIAL, PRACTICAL, etc.)
    - sessionDate: string (YYYY-MM-DD)
    - sessionTime: string (HH:MM)
    - checkInTime: timestamp
    - isPresent: boolean
    - confidenceScore: float (0.0 - 1.0)
    - location: string
    - createdAt: timestamp
```

## Current Features Implemented

### ✅ Completed
1. **Project Setup**: Firebase, TensorFlow Lite, Camera dependencies configured
2. **Database Schema**: Complete data models for Students, Staff, Attendance, and Courses
3. **Authentication System**: Login/Registration with email format detection
4. **Basic UI**: Login screen, Registration screen, Student/Staff dashboards
5. **Navigation**: Complete navigation flow between screens
6. **Dependency Injection**: Hilt setup for clean architecture

### 🚧 Next Steps (Coming Soon)
1. **Face Scanning**: TensorFlow Lite integration with liveness detection
2. **Face Recognition Engine**: Face matching and attendance marking
3. **Lecturer Dashboard**: Attendance management and mark allocation
4. **Testing**: Comprehensive testing of all features

## How to Test Current Features

### Test Student Registration
1. Open the app
2. Click "New Student? Register Here"
3. Fill in the form:
   - Student Number: 8 digits (e.g., 12345678)
   - Name: Your first name
   - Surname: Your last name
   - Faculty: Select from dropdown
   - Course: Enter course name
   - Check "I wear glasses" if applicable
4. Click "Continue to Face Scan"
5. You'll see generated credentials at the bottom

### Test Login
1. Use the generated credentials from registration
2. Email format: `{studentNumber}@dut4life.ac.za`
3. Password format: `$$Dut{first6digitsOfStudentNumber}`
4. Example: Email: `<EMAIL>`, Password: `$$Dut123456`

### Email Format Detection
- **Student emails**: 8 digits + @dut4life.ac.za (e.g., <EMAIL>)
- **Staff emails**: Custom name + @dut4life.ac.za (e.g., <EMAIL>)

## Security Notes
- Passwords are automatically generated for students using the specified format
- Staff passwords are set by administrators
- All data is stored securely in Firebase
- Face data will be encrypted when implemented

## Troubleshooting

### Build Issues
1. Make sure you've replaced the placeholder `google-services.json` with your actual file
2. Sync the project after adding the file
3. Clean and rebuild if needed

### Firebase Connection Issues
1. Verify your package name matches exactly: `com.example.facetrack`
2. Check that Authentication and Firestore are enabled in Firebase Console
3. Ensure your app is connected to the internet

### Login Issues
1. Make sure you've registered the student first
2. Check email format is correct (8 digits for students)
3. Verify password format: `$$Dut` + first 6 digits of student number
