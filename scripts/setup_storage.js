const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  storageBucket: `${serviceAccount.project_id}.appspot.com`
});

const bucket = admin.storage().bucket();

async function setupStorage() {
  console.log('🚀 Starting Firebase Storage setup...');
  
  try {
    // Create folder structure by uploading placeholder files
    const folders = [
      'face_images/students/',
      'face_images/staff/',
      'face_images/temp/',
      'attendance_photos/',
      'profile_pictures/'
    ];

    console.log('📁 Creating folder structure...');
    
    for (const folder of folders) {
      const fileName = `${folder}.gitkeep`;
      const file = bucket.file(fileName);
      
      // Create a placeholder file to establish the folder
      await file.save('# This file maintains the folder structure\n', {
        metadata: {
          contentType: 'text/plain'
        }
      });
      
      console.log(`✅ Created folder: ${folder}`);
    }

    // Set up Storage Rules (this will be displayed for manual setup)
    console.log('\n🔐 Storage Security Rules Setup:');
    console.log('Please manually add these rules in Firebase Console > Storage > Rules:');
    console.log('\n' + '='.repeat(60));
    console.log(`rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to read/write their own face images
    match /face_images/students/{studentId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email.matches('.*@dut4life.ac.za');
    }
    
    // Allow staff to read/write face images
    match /face_images/staff/{staffId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email.matches('.*@dut4life.ac.za');
    }
    
    // Allow temporary uploads during registration
    match /face_images/temp/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
    
    // Allow attendance photos
    match /attendance_photos/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email.matches('.*@dut4life.ac.za');
    }
    
    // Allow profile pictures
    match /profile_pictures/{allPaths=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.email.matches('.*@dut4life.ac.za');
    }
  }
}`);
    console.log('='.repeat(60));

    // Upload sample face images (create dummy images)
    console.log('\n📸 Creating sample face image placeholders...');
    
    const sampleStudents = ['12345678', '87654321', '11223344'];
    const sampleStaff = ['STAFF001', 'STAFF002'];
    
    // Create sample student face images
    for (const studentId of sampleStudents) {
      const faceImagePath = `face_images/students/${studentId}/face_without_glasses.jpg`;
      const faceImageWithGlassesPath = `face_images/students/${studentId}/face_with_glasses.jpg`;
      
      const sampleImageData = Buffer.from('Sample face image data - replace with actual image');
      
      await bucket.file(faceImagePath).save(sampleImageData, {
        metadata: {
          contentType: 'image/jpeg',
          customMetadata: {
            studentId: studentId,
            imageType: 'face_without_glasses',
            uploadDate: new Date().toISOString()
          }
        }
      });
      
      await bucket.file(faceImageWithGlassesPath).save(sampleImageData, {
        metadata: {
          contentType: 'image/jpeg',
          customMetadata: {
            studentId: studentId,
            imageType: 'face_with_glasses',
            uploadDate: new Date().toISOString()
          }
        }
      });
      
      console.log(`✅ Created face images for student: ${studentId}`);
    }
    
    // Create sample staff face images
    for (const staffId of sampleStaff) {
      const faceImagePath = `face_images/staff/${staffId}/face_image.jpg`;
      
      const sampleImageData = Buffer.from('Sample staff face image data - replace with actual image');
      
      await bucket.file(faceImagePath).save(sampleImageData, {
        metadata: {
          contentType: 'image/jpeg',
          customMetadata: {
            staffId: staffId,
            imageType: 'face_image',
            uploadDate: new Date().toISOString()
          }
        }
      });
      
      console.log(`✅ Created face image for staff: ${staffId}`);
    }

    console.log('\n🎉 Firebase Storage setup completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Folders created: ${folders.length}`);
    console.log(`   Student face images: ${sampleStudents.length * 2}`);
    console.log(`   Staff face images: ${sampleStaff.length}`);
    console.log('\n⚠️  Important: Please manually set up the Storage Rules in Firebase Console!');
    console.log('   Go to: Firebase Console > Storage > Rules > Copy the rules shown above');
    
  } catch (error) {
    console.error('❌ Error setting up storage:', error);
    process.exit(1);
  }
}

// Run the setup
setupStorage().then(() => {
  console.log('\n✅ Storage setup complete!');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Setup failed:', error);
  process.exit(1);
});
