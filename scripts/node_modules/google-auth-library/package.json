{"name": "google-auth-library", "version": "9.15.1", "author": "Google Inc.", "description": "Google APIs Authentication Client Library for Node.js", "engines": {"node": ">=14"}, "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "repository": "googleapis/google-auth-library-nodejs.git", "keywords": ["google", "api", "google apis", "client", "client library"], "dependencies": {"base64-js": "^1.3.0", "ecdsa-sig-formatter": "^1.0.11", "gaxios": "^6.1.1", "gcp-metadata": "^6.1.0", "gtoken": "^7.0.0", "jws": "^4.0.0"}, "devDependencies": {"@types/base64-js": "^1.2.5", "@types/chai": "^4.1.7", "@types/jws": "^3.1.0", "@types/mocha": "^9.0.0", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^20.4.2", "@types/sinon": "^17.0.0", "assert-rejects": "^1.0.0", "c8": "^8.0.0", "chai": "^4.2.0", "cheerio": "1.0.0-rc.12", "codecov": "^3.0.2", "engine.io": "6.6.2", "gts": "^5.0.0", "is-docker": "^2.0.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "karma": "^6.0.0", "karma-chrome-launcher": "^3.0.0", "karma-coverage": "^2.0.0", "karma-firefox-launcher": "^2.0.0", "karma-mocha": "^2.0.0", "karma-sourcemap-loader": "^0.4.0", "karma-webpack": "5.0.0", "keypair": "^1.0.4", "linkinator": "^4.0.0", "mocha": "^9.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "nock": "^13.0.0", "null-loader": "^4.0.0", "pdfmake": "0.2.12", "puppeteer": "^21.0.0", "sinon": "^18.0.0", "ts-loader": "^8.0.0", "typescript": "^5.1.6", "webpack": "^5.21.2", "webpack-cli": "^4.0.0"}, "files": ["build/src", "!build/src/**/*.map"], "scripts": {"test": "c8 mocha build/test", "clean": "gts clean", "prepare": "npm run compile", "lint": "gts check", "compile": "tsc -p .", "fix": "gts fix", "pretest": "npm run compile -- --sourceMap", "docs": "jsdoc -c .jsdoc.json", "samples-setup": "cd samples/ && npm link ../ && npm run setup && cd ../", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test --timeout 60000", "presystem-test": "npm run compile -- --sourceMap", "webpack": "webpack", "browser-test": "karma start", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean"}, "license": "Apache-2.0"}