const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// You'll need to download your service account key from Firebase Console
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://facetrack-attendance-default-rtdb.firebaseio.com"
});

const db = admin.firestore();

// Sample data for testing
const sampleStudents = [
  {
    studentNumber: "********",
    name: "<PERSON>",
    surname: "<PERSON><PERSON>",
    email: "<EMAIL>",
    password: "$$Dut123456",
    faculty: "Faculty of Engineering",
    course: "Computer Science",
    faceDataWithoutGlasses: "placeholder_face_data_1",
    faceDataWithGlasses: "",
    hasGlasses: false,
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    studentNumber: "********",
    name: "<PERSON>",
    surname: "<PERSON>",
    email: "<EMAIL>",
    password: "$$Dut876543",
    faculty: "Faculty of Applied Sciences",
    course: "Information Technology",
    faceDataWithoutGlasses: "placeholder_face_data_2",
    faceDataWithGlasses: "placeholder_face_data_2_glasses",
    hasGlasses: true,
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    studentNumber: "11223344",
    name: "Mike",
    surname: "Johnson",
    email: "<EMAIL>",
    password: "$$Dut112233",
    faculty: "Faculty of Engineering",
    course: "Electrical Engineering",
    faceDataWithoutGlasses: "placeholder_face_data_3",
    faceDataWithGlasses: "",
    hasGlasses: false,
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  }
];

const sampleStaff = [
  {
    staffId: "STAFF001",
    name: "Dr. Sarah",
    surname: "Wilson",
    email: "<EMAIL>",
    password: "SecurePass123!",
    department: "Computer Science",
    faculty: "Faculty of Applied Sciences",
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    staffId: "STAFF002",
    name: "Prof. David",
    surname: "Brown",
    email: "<EMAIL>",
    password: "SecurePass456!",
    department: "Electrical Engineering",
    faculty: "Faculty of Engineering",
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  }
];

const sampleCourses = [
  {
    courseCode: "CS101",
    courseName: "Introduction to Computer Science",
    faculty: "Faculty of Applied Sciences",
    department: "Computer Science",
    lecturerStaffId: "STAFF001",
    lecturerName: "Dr. Sarah Wilson",
    credits: 3,
    semester: 1,
    year: 2024,
    enrolledStudents: ["********", "********"],
    sessionSchedule: [
      {
        sessionType: "LECTURE",
        dayOfWeek: "MONDAY",
        startTime: "08:00",
        endTime: "09:00",
        venue: "Room A101",
        isRecurring: true
      },
      {
        sessionType: "PRACTICAL",
        dayOfWeek: "WEDNESDAY",
        startTime: "14:00",
        endTime: "16:00",
        venue: "Lab B201",
        isRecurring: true
      }
    ],
    attendanceWeight: 10.0,
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  },
  {
    courseCode: "EE201",
    courseName: "Circuit Analysis",
    faculty: "Faculty of Engineering",
    department: "Electrical Engineering",
    lecturerStaffId: "STAFF002",
    lecturerName: "Prof. David Brown",
    credits: 4,
    semester: 1,
    year: 2024,
    enrolledStudents: ["11223344"],
    sessionSchedule: [
      {
        sessionType: "LECTURE",
        dayOfWeek: "TUESDAY",
        startTime: "10:00",
        endTime: "11:00",
        venue: "Room C301",
        isRecurring: true
      },
      {
        sessionType: "TUTORIAL",
        dayOfWeek: "THURSDAY",
        startTime: "13:00",
        endTime: "14:00",
        venue: "Room C302",
        isRecurring: true
      }
    ],
    attendanceWeight: 15.0,
    isActive: true,
    createdAt: admin.firestore.Timestamp.now(),
    updatedAt: admin.firestore.Timestamp.now()
  }
];

// Sample attendance records
const sampleAttendance = [
  {
    studentNumber: "********",
    studentName: "John",
    studentSurname: "Doe",
    courseCode: "CS101",
    courseName: "Introduction to Computer Science",
    lecturerStaffId: "STAFF001",
    lecturerName: "Dr. Sarah Wilson",
    sessionType: "LECTURE",
    sessionDate: "2024-01-15",
    sessionTime: "08:00",
    checkInTime: admin.firestore.Timestamp.fromDate(new Date("2024-01-15T08:05:00")),
    isPresent: true,
    confidenceScore: 0.95,
    location: "Room A101",
    createdAt: admin.firestore.Timestamp.now()
  },
  {
    studentNumber: "********",
    studentName: "Jane",
    studentSurname: "Smith",
    courseCode: "CS101",
    courseName: "Introduction to Computer Science",
    lecturerStaffId: "STAFF001",
    lecturerName: "Dr. Sarah Wilson",
    sessionType: "LECTURE",
    sessionDate: "2024-01-15",
    sessionTime: "08:00",
    checkInTime: admin.firestore.Timestamp.fromDate(new Date("2024-01-15T08:03:00")),
    isPresent: true,
    confidenceScore: 0.92,
    location: "Room A101",
    createdAt: admin.firestore.Timestamp.now()
  }
];

async function setupFirestore() {
  try {
    console.log('🚀 Starting Firestore database setup...');

    // Create Students collection
    console.log('📚 Creating Students collection...');
    for (const student of sampleStudents) {
      await db.collection('students').doc(student.studentNumber).set(student);
      console.log(`✅ Added student: ${student.name} ${student.surname} (${student.studentNumber})`);
    }

    // Create Staff collection
    console.log('👨‍🏫 Creating Staff collection...');
    for (const staff of sampleStaff) {
      await db.collection('staff').doc(staff.staffId).set(staff);
      console.log(`✅ Added staff: ${staff.name} ${staff.surname} (${staff.staffId})`);
    }

    // Create Courses collection
    console.log('📖 Creating Courses collection...');
    for (const course of sampleCourses) {
      await db.collection('courses').doc(course.courseCode).set(course);
      console.log(`✅ Added course: ${course.courseName} (${course.courseCode})`);
    }

    // Create Attendance collection
    console.log('📋 Creating Attendance collection...');
    for (const attendance of sampleAttendance) {
      await db.collection('attendance').add(attendance);
      console.log(`✅ Added attendance record for: ${attendance.studentName} ${attendance.studentSurname}`);
    }

    console.log('🎉 Firestore database setup completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Students: ${sampleStudents.length}`);
    console.log(`   Staff: ${sampleStaff.length}`);
    console.log(`   Courses: ${sampleCourses.length}`);
    console.log(`   Attendance Records: ${sampleAttendance.length}`);
    
    console.log('\n🔐 Test Login Credentials:');
    sampleStudents.forEach(student => {
      console.log(`   Student: ${student.email} / ${student.password}`);
    });
    sampleStaff.forEach(staff => {
      console.log(`   Staff: ${staff.email} / ${staff.password}`);
    });

  } catch (error) {
    console.error('❌ Error setting up Firestore:', error);
  } finally {
    process.exit(0);
  }
}

// Run the setup
setupFirestore();
