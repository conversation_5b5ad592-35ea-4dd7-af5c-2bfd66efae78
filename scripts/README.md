# Firestore Database Setup Scripts

These scripts help you automatically generate and populate your Firestore database with sample data for testing the FaceTrack attendance system.

## Prerequisites

1. **Node.js** installed on your system
2. **Firebase project** set up with Firestore enabled
3. **Service Account Key** from Firebase Console

## Setup Instructions

### Step 1: Get Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to **Project Settings** (gear icon)
4. Click on **Service accounts** tab
5. Click **Generate new private key**
6. Download the JSON file
7. Rename it to `serviceAccountKey.json`
8. Place it in the `scripts/` folder

### Step 2: Install Dependencies

```bash
cd scripts
npm install
```

### Step 3: Run Setup Script

```bash
npm run setup
```

This will create and populate:
- **Students collection** with 3 sample students
- **Staff collection** with 2 sample lecturers
- **Courses collection** with 2 sample courses
- **Attendance collection** with sample attendance records

## Sample Data Created

### Students
| Student Number | Name | Email | Password | Faculty |
|----------------|------|-------|----------|---------|
| ******** | John Doe | <EMAIL> | $$Dut123456 | Engineering |
| ******** | Jane Smith | <EMAIL> | $$Dut876543 | Applied Sciences |
| ******** | Mike Johnson | <EMAIL> | $$Dut112233 | Engineering |

### Staff
| Staff ID | Name | Email | Password | Department |
|----------|------|-------|----------|------------|
| STAFF001 | Dr. Sarah Wilson | <EMAIL> | SecurePass123! | Computer Science |
| STAFF002 | Prof. David Brown | <EMAIL> | SecurePass456! | Electrical Engineering |

### Courses
| Course Code | Course Name | Lecturer | Enrolled Students |
|-------------|-------------|----------|-------------------|
| CS101 | Introduction to Computer Science | Dr. Sarah Wilson | John Doe, Jane Smith |
| EE201 | Circuit Analysis | Prof. David Brown | Mike Johnson |

## Clear Database

To clear all data from Firestore:

```bash
npm run clear
```

⚠️ **Warning**: This will permanently delete all data in the collections!

## Testing the App

After running the setup script, you can test the app with these credentials:

### Student Login
- **Email**: `<EMAIL>`
- **Password**: `$$Dut123456`

### Staff Login
- **Email**: `<EMAIL>`
- **Password**: `SecurePass123!`

## Customizing Sample Data

You can modify the sample data in `setup_firestore.js`:

- **sampleStudents**: Add/modify student records
- **sampleStaff**: Add/modify staff records
- **sampleCourses**: Add/modify course information
- **sampleAttendance**: Add/modify attendance records

## Security Rules

For production, make sure to update your Firestore security rules. The current test mode allows all reads/writes, which is not secure for production use.

Example production rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Students can only read/write their own data
    match /students/{studentId} {
      allow read, write: if request.auth != null && request.auth.token.email == resource.data.email;
    }
    
    // Staff can read student data for their courses
    match /staff/{staffId} {
      allow read, write: if request.auth != null && request.auth.token.email == resource.data.email;
    }
    
    // Attendance records - students can read their own, staff can read/write for their courses
    match /attendance/{attendanceId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        exists(/databases/$(database)/documents/staff/$(request.auth.uid));
    }
  }
}
```

## Troubleshooting

### Error: "Could not load the default credentials"
- Make sure `serviceAccountKey.json` is in the `scripts/` folder
- Verify the file is valid JSON and not corrupted

### Error: "Permission denied"
- Check that Firestore is enabled in your Firebase project
- Verify your service account has the correct permissions

### Error: "Module not found"
- Run `npm install` in the scripts directory
- Make sure Node.js is installed

## File Structure
```
scripts/
├── setup_firestore.js      # Main setup script
├── clear_firestore.js      # Database cleanup script
├── package.json            # Node.js dependencies
├── serviceAccountKey.json  # Your Firebase service account key (add this)
└── README.md              # This file
```
