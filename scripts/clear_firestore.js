const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: "https://facetrack-attendance-default-rtdb.firebaseio.com"
});

const db = admin.firestore();

async function deleteCollection(collectionName) {
  const collectionRef = db.collection(collectionName);
  const query = collectionRef.limit(500);

  return new Promise((resolve, reject) => {
    deleteQueryBatch(query, resolve).catch(reject);
  });
}

async function deleteQueryBatch(query, resolve) {
  const snapshot = await query.get();

  const batchSize = snapshot.size;
  if (batchSize === 0) {
    resolve();
    return;
  }

  const batch = db.batch();
  snapshot.docs.forEach((doc) => {
    batch.delete(doc.ref);
  });

  await batch.commit();

  process.nextTick(() => {
    deleteQueryBatch(query, resolve);
  });
}

async function clearFirestore() {
  try {
    console.log('🧹 Starting Firestore database cleanup...');

    const collections = ['students', 'staff', 'courses', 'attendance'];

    for (const collectionName of collections) {
      console.log(`🗑️  Clearing ${collectionName} collection...`);
      await deleteCollection(collectionName);
      console.log(`✅ Cleared ${collectionName} collection`);
    }

    console.log('🎉 Firestore database cleared successfully!');
    
  } catch (error) {
    console.error('❌ Error clearing Firestore:', error);
  } finally {
    process.exit(0);
  }
}

// Run the cleanup
clearFirestore();
