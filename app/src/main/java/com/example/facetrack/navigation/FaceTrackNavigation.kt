package com.example.facetrack.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.facetrack.repository.UserType
import com.example.facetrack.ui.screens.*

/**
 * Navigation routes
 */
object Routes {
    const val SPLASH = "splash"
    const val LOGIN = "login"
    const val REGISTRATION = "registration"
    const val STUDENT_DASHBOARD = "student_dashboard"
    const val STAFF_DASHBOARD = "staff_dashboard"
    const val SIMPLE_DASHBOARD = "simple_dashboard"
    const val FACE_SCAN = "face_scan"
}

/**
 * Main navigation composable
 */
@Composable
fun FaceTrackNavigation(
    navController: NavHostController = rememberNavController(),
    startDestination: String = Routes.SPLASH
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Splash Screen
        composable(Routes.SPLASH) {
            SplashScreen(
                onSplashComplete = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.SPLASH) { inclusive = true }
                    }
                }
            )
        }
        // Login Screen
        composable(Routes.LOGIN) {
            LoginScreen(
                onNavigateToRegistration = {
                    navController.navigate(Routes.REGISTRATION)
                },
                onLoginSuccess = { userType ->
                    // For now, navigate to simple dashboard for both user types
                    navController.navigate("${Routes.SIMPLE_DASHBOARD}/${userType.name}/TestUser") {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        
        // Registration Screen
        composable(Routes.REGISTRATION) {
            RegistrationScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onRegistrationSuccess = { studentData ->
                    // Navigate to face scanning
                    navController.navigate("${Routes.FACE_SCAN}/${studentData.studentNumber}/${studentData.name}/${studentData.surname}/${studentData.password}/${studentData.faculty}/${studentData.course}/${studentData.hasGlasses}")
                }
            )
        }

        // Face Scan Screen
        composable("${Routes.FACE_SCAN}/{studentNumber}/{name}/{surname}/{password}/{faculty}/{course}/{hasGlasses}") { backStackEntry ->
            val studentNumber = backStackEntry.arguments?.getString("studentNumber") ?: ""
            val name = backStackEntry.arguments?.getString("name") ?: ""
            val surname = backStackEntry.arguments?.getString("surname") ?: ""
            val password = backStackEntry.arguments?.getString("password") ?: ""
            val faculty = backStackEntry.arguments?.getString("faculty") ?: ""
            val course = backStackEntry.arguments?.getString("course") ?: ""
            val hasGlasses = backStackEntry.arguments?.getString("hasGlasses")?.toBoolean() ?: false

            val studentData = StudentRegistrationData(
                studentNumber = studentNumber,
                name = name,
                surname = surname,
                password = password,
                faculty = faculty,
                course = course,
                hasGlasses = hasGlasses
            )

            FaceScanScreen(
                studentData = studentData,
                onNavigateBack = {
                    navController.popBackStack()
                },
                onScanComplete = { faceDataWithoutGlasses, faceDataWithGlasses ->
                    // TODO: Update student record in Firebase with actual face data
                    // For now, just navigate to login with success message
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.REGISTRATION) { inclusive = true }
                    }
                }
            )
        }

        // Simple Dashboard Screen
        composable("${Routes.SIMPLE_DASHBOARD}/{userType}/{userName}") { backStackEntry ->
            val userType = backStackEntry.arguments?.getString("userType") ?: "student"
            val userName = backStackEntry.arguments?.getString("userName") ?: "User"

            SimpleDashboardScreen(
                userType = userType.lowercase(),
                userName = userName,
                onNavigateToFaceScan = {
                    navController.navigate(Routes.FACE_SCAN)
                },
                onNavigateToAttendance = {
                    // TODO: Navigate to attendance screen
                },
                onLogout = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.SIMPLE_DASHBOARD) { inclusive = true }
                    }
                }
            )
        }

        // Student Dashboard
        composable(Routes.STUDENT_DASHBOARD) {
            StudentDashboardScreen(
                onLogout = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.STUDENT_DASHBOARD) { inclusive = true }
                    }
                }
            )
        }
        
        // Staff Dashboard
        composable(Routes.STAFF_DASHBOARD) {
            StaffDashboardScreen(
                onLogout = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.STAFF_DASHBOARD) { inclusive = true }
                    }
                }
            )
        }
    }
}
