package com.example.facetrack.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.facetrack.repository.UserType
import com.example.facetrack.ui.screens.LoginScreen
import com.example.facetrack.ui.screens.RegistrationScreen
import com.example.facetrack.ui.screens.StudentDashboardScreen
import com.example.facetrack.ui.screens.StaffDashboardScreen

/**
 * Navigation routes
 */
object Routes {
    const val LOGIN = "login"
    const val REGISTRATION = "registration"
    const val STUDENT_DASHBOARD = "student_dashboard"
    const val STAFF_DASHBOARD = "staff_dashboard"
    const val FACE_SCAN = "face_scan"
}

/**
 * Main navigation composable
 */
@Composable
fun FaceTrackNavigation(
    navController: NavHostController = rememberNavController(),
    startDestination: String = Routes.LOGIN
) {
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // Login Screen
        composable(Routes.LOGIN) {
            LoginScreen(
                onNavigateToRegistration = {
                    navController.navigate(Routes.REGISTRATION)
                },
                onLoginSuccess = { userType ->
                    when (userType) {
                        UserType.STUDENT -> {
                            navController.navigate(Routes.STUDENT_DASHBOARD) {
                                popUpTo(Routes.LOGIN) { inclusive = true }
                            }
                        }
                        UserType.STAFF -> {
                            navController.navigate(Routes.STAFF_DASHBOARD) {
                                popUpTo(Routes.LOGIN) { inclusive = true }
                            }
                        }
                    }
                }
            )
        }
        
        // Registration Screen
        composable(Routes.REGISTRATION) {
            RegistrationScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onRegistrationSuccess = {
                    // For now, navigate back to login
                    // Later we'll navigate to face scanning
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.REGISTRATION) { inclusive = true }
                    }
                }
            )
        }
        
        // Student Dashboard
        composable(Routes.STUDENT_DASHBOARD) {
            StudentDashboardScreen(
                onLogout = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.STUDENT_DASHBOARD) { inclusive = true }
                    }
                }
            )
        }
        
        // Staff Dashboard
        composable(Routes.STAFF_DASHBOARD) {
            StaffDashboardScreen(
                onLogout = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.STAFF_DASHBOARD) { inclusive = true }
                    }
                }
            )
        }
    }
}
