package com.example.facetrack.viewmodel

import android.content.Context
import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.facetrack.ml.FaceDetectionHelper
import com.example.facetrack.ml.FaceDetectionResult
import com.example.facetrack.ml.HeadMovement
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for face scanning functionality
 */
@HiltViewModel
class FaceScanViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : ViewModel() {
    
    private val faceDetectionHelper = FaceDetectionHelper(context)
    
    private val _scanState = MutableStateFlow(FaceScanState())
    val scanState: StateFlow<FaceScanState> = _scanState.asStateFlow()
    
    private var previousFaceResult: FaceDetectionResult? = null
    private val requiredMovements = mutableListOf(
        HeadMovement.CENTER,  // Start with center position
        HeadMovement.LEFT,
        HeadMovement.CENTER,
        HeadMovement.RIGHT,
        HeadMovement.CENTER
    )
    private var currentMovementIndex = 0
    
    init {
        initializeFaceDetection()
    }
    
    /**
     * Initialize face detection
     */
    private fun initializeFaceDetection() {
        viewModelScope.launch {
            val success = faceDetectionHelper.initializeDetector()
            _scanState.value = _scanState.value.copy(
                isInitialized = success,
                error = if (!success) "Failed to initialize face detection" else null
            )
        }
    }
    
    /**
     * Process camera frame for face detection
     */
    fun processCameraFrame(bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                println("FaceScan: Processing frame: ${bitmap.width}x${bitmap.height}")
                val faces = faceDetectionHelper.detectFaces(bitmap)
                println("FaceScan: Detected ${faces.size} faces")

                if (faces.isEmpty()) {
                    _scanState.value = _scanState.value.copy(
                        currentInstruction = "Position your face in the center of the circle",
                        faceDetected = false
                    )
                    return@launch
                }

                val face = faces.first()

                // Check if face is well positioned
                val isWellPositioned = faceDetectionHelper.isFaceLookingStraight(
                    face.boundingBox,
                    bitmap.width,
                    bitmap.height
                )

                _scanState.value = _scanState.value.copy(
                    faceDetected = true,
                    currentFaceResult = face,
                    currentInstruction = if (isWellPositioned) {
                        getInstructionForMovement(
                            if (currentMovementIndex < requiredMovements.size)
                                requiredMovements[currentMovementIndex]
                            else
                                HeadMovement.CENTER
                        )
                    } else {
                        "Center your face in the circle and look straight"
                    }
                )

                // Only process liveness if face is well positioned
                if (isWellPositioned) {
                    processLivenessDetection(face, bitmap)
                }

            } catch (e: Exception) {
                _scanState.value = _scanState.value.copy(
                    error = "Face detection error: ${e.message}"
                )
            }
        }
    }
    
    /**
     * REAL liveness detection based on actual head movements
     */
    private fun processLivenessDetection(face: FaceDetectionResult, bitmap: Bitmap) {
        if (currentMovementIndex >= requiredMovements.size) {
            // All movements completed
            completeLivenessDetection(face, bitmap)
            return
        }

        val requiredMovement = requiredMovements[currentMovementIndex]

        // REAL head movement detection with better logic
        val movementDetected = if (previousFaceResult != null) {
            val detected = faceDetectionHelper.detectHeadMovement(
                previousFaceResult?.boundingBox,
                face.boundingBox,
                requiredMovement
            )

            // Debug logging
            println("FaceScan: Required: $requiredMovement, Detected: $detected")
            println("FaceScan: Previous center: ${previousFaceResult?.boundingBox?.centerX()}, Current center: ${face.boundingBox.centerX()}")

            detected
        } else {
            // First frame - start with CENTER movement
            if (requiredMovement == HeadMovement.CENTER) {
                println("FaceScan: First frame - CENTER detected")
                true
            } else {
                false
            }
        }

        if (movementDetected) {
            currentMovementIndex++
            val progress = (currentMovementIndex.toFloat() / requiredMovements.size) * 100

            println("FaceScan: Movement completed! Progress: ${progress.toInt()}%")

            _scanState.value = _scanState.value.copy(
                livenessProgress = progress.toInt(),
                currentInstruction = if (currentMovementIndex < requiredMovements.size) {
                    getInstructionForMovement(requiredMovements[currentMovementIndex])
                } else {
                    "Perfect! Processing face data..."
                }
            )

            // Auto-complete after reaching 100%
            if (progress >= 100) {
                completeLivenessDetection(face, bitmap)
            }
        }

        previousFaceResult = face
    }
    
    /**
     * Complete liveness detection and extract face embedding
     */
    private fun completeLivenessDetection(face: FaceDetectionResult, bitmap: Bitmap) {
        viewModelScope.launch {
            try {
                val faceEmbedding = faceDetectionHelper.extractFaceEmbedding(
                    bitmap, 
                    face.boundingBox
                )
                
                _scanState.value = _scanState.value.copy(
                    isComplete = true,
                    faceEmbedding = faceEmbedding,
                    currentInstruction = "Face scan completed successfully!"
                )
                
            } catch (e: Exception) {
                _scanState.value = _scanState.value.copy(
                    error = "Failed to extract face data: ${e.message}"
                )
            }
        }
    }
    
    /**
     * Get instruction text for movement
     */
    private fun getInstructionForMovement(movement: HeadMovement): String {
        return when (movement) {
            HeadMovement.LEFT -> "Slowly turn your head to the left"
            HeadMovement.RIGHT -> "Slowly turn your head to the right"
            HeadMovement.UP -> "Slowly tilt your head up"
            HeadMovement.DOWN -> "Slowly tilt your head down"
            HeadMovement.CENTER -> "Return to center and look straight"
        }
    }
    
    /**
     * Reset scan state for new scan
     */
    fun resetScan() {
        currentMovementIndex = 0
        previousFaceResult = null
        _scanState.value = FaceScanState()
        initializeFaceDetection()
    }
    
    /**
     * Get face embedding as base64 string for storage
     */
    fun getFaceEmbeddingAsString(): String {
        val embedding = _scanState.value.faceEmbedding ?: return ""
        return embedding.joinToString(",") { it.toString() }
    }
    
    override fun onCleared() {
        super.onCleared()
        faceDetectionHelper.close()
    }
}

/**
 * State class for face scanning
 */
data class FaceScanState(
    val isInitialized: Boolean = false,
    val faceDetected: Boolean = false,
    val livenessProgress: Int = 0,
    val currentInstruction: String = "Initializing face detection...",
    val currentFaceResult: FaceDetectionResult? = null,
    val faceEmbedding: FloatArray? = null,
    val isComplete: Boolean = false,
    val error: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FaceScanState

        if (isInitialized != other.isInitialized) return false
        if (faceDetected != other.faceDetected) return false
        if (livenessProgress != other.livenessProgress) return false
        if (currentInstruction != other.currentInstruction) return false
        if (currentFaceResult != other.currentFaceResult) return false
        if (faceEmbedding != null) {
            if (other.faceEmbedding == null) return false
            if (!faceEmbedding.contentEquals(other.faceEmbedding)) return false
        } else if (other.faceEmbedding != null) return false
        if (isComplete != other.isComplete) return false
        if (error != other.error) return false

        return true
    }

    override fun hashCode(): Int {
        var result = isInitialized.hashCode()
        result = 31 * result + faceDetected.hashCode()
        result = 31 * result + livenessProgress
        result = 31 * result + currentInstruction.hashCode()
        result = 31 * result + (currentFaceResult?.hashCode() ?: 0)
        result = 31 * result + (faceEmbedding?.contentHashCode() ?: 0)
        result = 31 * result + isComplete.hashCode()
        result = 31 * result + (error?.hashCode() ?: 0)
        return result
    }
}
