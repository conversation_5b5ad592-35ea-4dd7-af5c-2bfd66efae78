package com.example.facetrack.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

data class CourseInfo(
    val courseId: String = "",
    val courseName: String = "",
    val courseCode: String = "",
    val time: String = "",
    val location: String = "",
    val lecturer: String = ""
)

data class AttendanceRecord(
    val courseCode: String = "",
    val courseName: String = "",
    val date: String = "",
    val time: String = "",
    val status: String = "", // "Present", "Absent", "Late"
    val checkInTime: String = ""
)

data class StudentDashboardUiState(
    val isLoading: Boolean = false,
    val studentName: String = "",
    val studentNumber: String = "",
    val todaysCourses: List<CourseInfo> = emptyList(),
    val recentAttendance: List<AttendanceRecord> = emptyList(),
    val totalClasses: Int = 0,
    val attendedClasses: Int = 0,
    val attendancePercentage: Float = 0f,
    val error: String? = null
)

@HiltViewModel
class StudentDashboardViewModel @Inject constructor(
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) : ViewModel() {

    private val _uiState = MutableStateFlow(StudentDashboardUiState())
    val uiState: StateFlow<StudentDashboardUiState> = _uiState.asStateFlow()

    private val currentUser = auth.currentUser
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())

    fun loadStudentData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val studentNumber = extractStudentNumber()
                if (studentNumber != null) {
                    loadStudentInfo(studentNumber)
                    loadTodaysCourses(studentNumber)
                    loadRecentAttendance(studentNumber)
                    loadAttendanceStats(studentNumber)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load student data: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    private fun extractStudentNumber(): String? {
        return currentUser?.email?.substringBefore("@")
    }

    private suspend fun loadStudentInfo(studentNumber: String) {
        try {
            val studentDoc = firestore.collection("students")
                .document(studentNumber)
                .get()
                .await()

            if (studentDoc.exists()) {
                val name = studentDoc.getString("name") ?: ""
                val surname = studentDoc.getString("surname") ?: ""
                
                _uiState.value = _uiState.value.copy(
                    studentName = "$name $surname",
                    studentNumber = studentNumber
                )
            }
        } catch (e: Exception) {
            println("Error loading student info: ${e.message}")
        }
    }

    private suspend fun loadTodaysCourses(studentNumber: String) {
        try {
            // Get student's enrolled courses from their document
            val studentDoc = firestore.collection("students")
                .document(studentNumber)
                .get()
                .await()

            if (studentDoc.exists()) {
                val faculty = studentDoc.getString("faculty") ?: ""
                val course = studentDoc.getString("course") ?: ""

                // Load courses from the courses collection that match student's program
                val coursesQuery = firestore.collection("courses")
                    .get()
                    .await()

                val courses = coursesQuery.documents.mapNotNull { doc ->
                    try {
                        CourseInfo(
                            courseId = doc.id,
                            courseName = doc.getString("courseName") ?: "",
                            courseCode = doc.getString("courseCode") ?: "",
                            time = "08:00 - 10:00", // Default time - in real app this would come from schedule
                            location = "TBD", // Default location
                            lecturer = doc.getString("lecturer") ?: "TBD"
                        )
                    } catch (e: Exception) {
                        null
                    }
                }

                _uiState.value = _uiState.value.copy(todaysCourses = courses)
            } else {
                // New student with no courses yet
                _uiState.value = _uiState.value.copy(todaysCourses = emptyList())
            }
        } catch (e: Exception) {
            println("Error loading today's courses: ${e.message}")
            _uiState.value = _uiState.value.copy(todaysCourses = emptyList())
        }
    }

    private suspend fun loadRecentAttendance(studentNumber: String) {
        try {
            val attendanceQuery = firestore.collection("attendance")
                .whereEqualTo("studentNumber", studentNumber)
                .orderBy("timestamp", Query.Direction.DESCENDING)
                .limit(10)
                .get()
                .await()

            val attendanceRecords = attendanceQuery.documents.mapNotNull { doc ->
                try {
                    val timestamp = doc.getTimestamp("timestamp")
                    val date = timestamp?.let { dateFormat.format(it.toDate()) } ?: ""
                    val time = timestamp?.let { timeFormat.format(it.toDate()) } ?: ""
                    
                    AttendanceRecord(
                        courseCode = doc.getString("courseCode") ?: "",
                        courseName = doc.getString("courseName") ?: "",
                        date = date,
                        time = time,
                        status = doc.getString("status") ?: "Present",
                        checkInTime = time
                    )
                } catch (e: Exception) {
                    null
                }
            }

            _uiState.value = _uiState.value.copy(recentAttendance = attendanceRecords)
        } catch (e: Exception) {
            println("Error loading recent attendance: ${e.message}")
        }
    }

    private suspend fun loadAttendanceStats(studentNumber: String) {
        try {
            val attendanceQuery = firestore.collection("attendance")
                .whereEqualTo("studentNumber", studentNumber)
                .get()
                .await()

            val totalClasses = attendanceQuery.size()
            val attendedClasses = attendanceQuery.documents.count { doc ->
                doc.getString("status") == "Present"
            }

            val percentage = if (totalClasses > 0) {
                (attendedClasses.toFloat() / totalClasses.toFloat()) * 100f
            } else {
                0f
            }

            _uiState.value = _uiState.value.copy(
                totalClasses = totalClasses,
                attendedClasses = attendedClasses,
                attendancePercentage = percentage,
                isLoading = false
            )
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = "Failed to load attendance stats: ${e.message}",
                isLoading = false
            )
        }
    }

    fun checkInToClass() {
        viewModelScope.launch {
            try {
                val studentNumber = extractStudentNumber() ?: return@launch
                val currentTime = System.currentTimeMillis()

                // Get the first available course for check-in (in real app, user would select)
                val availableCourse = _uiState.value.todaysCourses.firstOrNull()

                if (availableCourse != null) {
                    // Create attendance record with actual course
                    val attendanceData = hashMapOf(
                        "studentNumber" to studentNumber,
                        "courseCode" to availableCourse.courseCode,
                        "courseName" to availableCourse.courseName,
                        "timestamp" to com.google.firebase.Timestamp(Date(currentTime)),
                        "status" to "Present",
                        "checkInMethod" to "Manual"
                    )

                    firestore.collection("attendance")
                        .add(attendanceData)
                        .await()

                    // Refresh data
                    loadRecentAttendance(studentNumber)
                    loadAttendanceStats(studentNumber)
                } else {
                    _uiState.value = _uiState.value.copy(
                        error = "No courses available for check-in. Please contact your administrator."
                    )
                }

            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to check in: ${e.message}"
                )
            }
        }
    }

    fun checkOutFromClass() {
        // Implementation for check out (if needed)
        // For now, just show a message
        _uiState.value = _uiState.value.copy(
            error = "Check out functionality coming soon!"
        )
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
