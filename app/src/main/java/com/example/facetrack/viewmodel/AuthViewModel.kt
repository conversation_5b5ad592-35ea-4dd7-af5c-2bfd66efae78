package com.example.facetrack.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.facetrack.data.Student
import com.example.facetrack.repository.AuthRepository
import com.example.facetrack.repository.UserType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for handling authentication operations
 */
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AuthUiState())
    val uiState: StateFlow<AuthUiState> = _uiState.asStateFlow()
    
    private val _loginState = MutableStateFlow<LoginState>(LoginState.Idle)
    val loginState: StateFlow<LoginState> = _loginState.asStateFlow()
    
    private val _registrationState = MutableStateFlow<RegistrationState>(RegistrationState.Idle)
    val registrationState: StateFlow<RegistrationState> = _registrationState.asStateFlow()
    
    /**
     * Login user
     */
    fun login(email: String, password: String) {
        viewModelScope.launch {
            _loginState.value = LoginState.Loading
            
            // Validate email format
            if (!isValidEmail(email)) {
                _loginState.value = LoginState.Error("Invalid email format")
                return@launch
            }
            
            val result = authRepository.login(email, password)
            result.fold(
                onSuccess = { userType ->
                    _loginState.value = LoginState.Success(userType)
                },
                onFailure = { exception ->
                    _loginState.value = LoginState.Error(exception.message ?: "Login failed")
                }
            )
        }
    }
    
    /**
     * Register new student
     */
    fun registerStudent(
        studentNumber: String,
        name: String,
        surname: String,
        faculty: String,
        course: String,
        faceDataWithoutGlasses: String,
        faceDataWithGlasses: String = "",
        hasGlasses: Boolean = false
    ) {
        viewModelScope.launch {
            _registrationState.value = RegistrationState.Loading
            
            // Validate student number (8 digits)
            if (!studentNumber.matches(Regex("\\d{8}"))) {
                _registrationState.value = RegistrationState.Error("Student number must be 8 digits")
                return@launch
            }
            
            // Generate email and password
            val email = "${studentNumber}@dut4life.ac.za"
            val password = "\$\$Dut${studentNumber.take(6)}"
            
            val student = Student(
                studentNumber = studentNumber,
                name = name,
                surname = surname,
                email = email,
                password = password,
                faculty = faculty,
                course = course,
                faceDataWithoutGlasses = faceDataWithoutGlasses,
                faceDataWithGlasses = faceDataWithGlasses,
                hasGlasses = hasGlasses
            )
            
            val result = authRepository.registerStudent(student)
            result.fold(
                onSuccess = { studentId ->
                    _registrationState.value = RegistrationState.Success(studentId)
                },
                onFailure = { exception ->
                    _registrationState.value = RegistrationState.Error(
                        exception.message ?: "Registration failed"
                    )
                }
            )
        }
    }
    
    /**
     * Logout current user
     */
    fun logout() {
        authRepository.logout()
        _loginState.value = LoginState.Idle
        _registrationState.value = RegistrationState.Idle
    }
    
    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        return authRepository.isLoggedIn()
    }
    
    /**
     * Reset login state
     */
    fun resetLoginState() {
        _loginState.value = LoginState.Idle
    }
    
    /**
     * Reset registration state
     */
    fun resetRegistrationState() {
        _registrationState.value = RegistrationState.Idle
    }
    
    /**
     * Validate email format
     */
    private fun isValidEmail(email: String): Boolean {
        val dutEmailPattern = Regex("^[a-zA-Z0-9]+@dut4life\\.ac\\.za$")
        return dutEmailPattern.matches(email)
    }
    
    /**
     * Determine if email is student format (8 digits)
     */
    fun isStudentEmail(email: String): Boolean {
        val localPart = email.substringBefore("@")
        return localPart.matches(Regex("\\d{8}"))
    }
}

/**
 * UI State for authentication screens
 */
data class AuthUiState(
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Login state sealed class
 */
sealed class LoginState {
    object Idle : LoginState()
    object Loading : LoginState()
    data class Success(val userType: UserType) : LoginState()
    data class Error(val message: String) : LoginState()
}

/**
 * Registration state sealed class
 */
sealed class RegistrationState {
    object Idle : RegistrationState()
    object Loading : RegistrationState()
    data class Success(val studentId: String) : RegistrationState()
    data class Error(val message: String) : RegistrationState()
}
