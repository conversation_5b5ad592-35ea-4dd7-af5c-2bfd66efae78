package com.example.facetrack.repository

import com.example.facetrack.data.Student
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for handling Student-related database operations
 */
@Singleton
class StudentRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {
    
    /**
     * Save student to Firestore
     */
    suspend fun saveStudent(student: Student): Result<Unit> {
        return try {
            firestore.collection("students")
                .document(student.studentNumber)
                .set(student)
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get student by student number
     */
    suspend fun getStudent(studentNumber: String): Result<Student> {
        return try {
            val document = firestore.collection("students")
                .document(studentNumber)
                .get()
                .await()
            
            if (document.exists()) {
                val student = document.toObject(Student::class.java)
                Result.success(student ?: Student())
            } else {
                Result.failure(Exception("Student not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update student information
     */
    suspend fun updateStudent(student: Student): Result<Unit> {
        return try {
            val updates = mapOf(
                "name" to student.name,
                "surname" to student.surname,
                "faculty" to student.faculty,
                "course" to student.course,
                "faceDataWithoutGlasses" to student.faceDataWithoutGlasses,
                "faceDataWithGlasses" to student.faceDataWithGlasses,
                "hasGlasses" to student.hasGlasses,
                "updatedAt" to System.currentTimeMillis()
            )
            
            firestore.collection("students")
                .document(student.studentNumber)
                .update(updates)
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Check if student exists
     */
    suspend fun studentExists(studentNumber: String): Boolean {
        return try {
            val document = firestore.collection("students")
                .document(studentNumber)
                .get()
                .await()
            document.exists()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get all students (for admin purposes)
     */
    suspend fun getAllStudents(): Result<List<Student>> {
        return try {
            val querySnapshot = firestore.collection("students")
                .whereEqualTo("isActive", true)
                .get()
                .await()
            
            val students = querySnapshot.documents.mapNotNull { document ->
                document.toObject(Student::class.java)
            }
            
            Result.success(students)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get students by faculty
     */
    suspend fun getStudentsByFaculty(faculty: String): Result<List<Student>> {
        return try {
            val querySnapshot = firestore.collection("students")
                .whereEqualTo("faculty", faculty)
                .whereEqualTo("isActive", true)
                .get()
                .await()
            
            val students = querySnapshot.documents.mapNotNull { document ->
                document.toObject(Student::class.java)
            }
            
            Result.success(students)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update student face data
     */
    suspend fun updateFaceData(
        studentNumber: String,
        faceDataWithoutGlasses: String,
        faceDataWithGlasses: String = "",
        hasGlasses: Boolean = false
    ): Result<Unit> {
        return try {
            val updates = mapOf(
                "faceDataWithoutGlasses" to faceDataWithoutGlasses,
                "faceDataWithGlasses" to faceDataWithGlasses,
                "hasGlasses" to hasGlasses,
                "updatedAt" to System.currentTimeMillis()
            )
            
            firestore.collection("students")
                .document(studentNumber)
                .update(updates)
                .await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
