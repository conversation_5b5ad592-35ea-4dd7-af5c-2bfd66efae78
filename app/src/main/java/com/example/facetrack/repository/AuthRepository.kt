package com.example.facetrack.repository

import com.example.facetrack.data.Staff
import com.example.facetrack.data.Student
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for handling authentication operations
 */
@Singleton
class AuthRepository @Inject constructor(
    private val firebaseAuth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) {
    
    /**
     * Login user based on email format
     * Students: <EMAIL>
     * Staff: <EMAIL>
     */
    suspend fun login(email: String, password: String): Result<UserType> {
        return try {
            // Authenticate with Firebase Auth
            val authResult = firebaseAuth.signInWithEmailAndPassword(email, password).await()
            val user = authResult.user
            
            if (user != null) {
                // Determine user type based on email format
                val userType = determineUserType(email)
                
                // Verify user exists in appropriate collection
                when (userType) {
                    UserType.STUDENT -> {
                        val studentNumber = extractStudentNumber(email)
                        val studentDoc = firestore.collection("students")
                            .document(studentNumber)
                            .get()
                            .await()
                        
                        if (studentDoc.exists()) {
                            Result.success(UserType.STUDENT)
                        } else {
                            Result.failure(Exception("Student record not found"))
                        }
                    }
                    UserType.STAFF -> {
                        // For staff, we need to find by email since staffId might be different
                        val staffQuery = firestore.collection("staff")
                            .whereEqualTo("email", email)
                            .get()
                            .await()
                        
                        if (!staffQuery.isEmpty) {
                            Result.success(UserType.STAFF)
                        } else {
                            Result.failure(Exception("Staff record not found"))
                        }
                    }
                }
            } else {
                Result.failure(Exception("Authentication failed"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Register a new student
     */
    suspend fun registerStudent(student: Student): Result<String> {
        return try {
            // Create Firebase Auth account
            val authResult = firebaseAuth.createUserWithEmailAndPassword(
                student.email, 
                student.password
            ).await()
            
            val user = authResult.user
            if (user != null) {
                // Save student data to Firestore
                firestore.collection("students")
                    .document(student.studentNumber)
                    .set(student)
                    .await()
                
                Result.success(student.studentNumber)
            } else {
                Result.failure(Exception("Failed to create user account"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get current user information
     */
    suspend fun getCurrentUser(): Result<Any> {
        return try {
            val currentUser = firebaseAuth.currentUser
            if (currentUser != null) {
                val email = currentUser.email ?: ""
                val userType = determineUserType(email)
                
                when (userType) {
                    UserType.STUDENT -> {
                        val studentNumber = extractStudentNumber(email)
                        val studentDoc = firestore.collection("students")
                            .document(studentNumber)
                            .get()
                            .await()
                        
                        if (studentDoc.exists()) {
                            val student = studentDoc.toObject(Student::class.java)
                            Result.success(student ?: Student())
                        } else {
                            Result.failure(Exception("Student not found"))
                        }
                    }
                    UserType.STAFF -> {
                        val staffQuery = firestore.collection("staff")
                            .whereEqualTo("email", email)
                            .get()
                            .await()
                        
                        if (!staffQuery.isEmpty) {
                            val staff = staffQuery.documents[0].toObject(Staff::class.java)
                            Result.success(staff ?: Staff())
                        } else {
                            Result.failure(Exception("Staff not found"))
                        }
                    }
                }
            } else {
                Result.failure(Exception("No user logged in"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Logout current user
     */
    fun logout() {
        firebaseAuth.signOut()
    }
    
    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        return firebaseAuth.currentUser != null
    }
    
    /**
     * Determine user type based on email format
     */
    private fun determineUserType(email: String): UserType {
        val localPart = email.substringBefore("@")
        return if (localPart.matches(Regex("\\d{8}"))) {
            UserType.STUDENT
        } else {
            UserType.STAFF
        }
    }
    
    /**
     * Extract student number from email
     */
    private fun extractStudentNumber(email: String): String {
        return email.substringBefore("@")
    }
}

/**
 * Enum for user types
 */
enum class UserType {
    STUDENT,
    STAFF
}
