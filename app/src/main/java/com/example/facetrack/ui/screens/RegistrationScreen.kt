package com.example.facetrack.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.facetrack.data.Faculty
import com.example.facetrack.viewmodel.AuthViewModel
import com.example.facetrack.viewmodel.RegistrationState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegistrationScreen(
    onNavigateBack: () -> Unit,
    onRegistrationSuccess: () -> Unit,
    viewModel: AuthViewModel = hiltViewModel()
) {
    var studentNumber by remember { mutableStateOf("") }
    var name by remember { mutableStateOf("") }
    var surname by remember { mutableStateOf("") }
    var selectedFaculty by remember { mutableStateOf(Faculty.ENGINEERING) }
    var course by remember { mutableStateOf("") }
    var hasGlasses by remember { mutableStateOf(false) }
    var showFacultyDropdown by remember { mutableStateOf(false) }
    
    val registrationState by viewModel.registrationState.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()
    
    // Handle registration state changes
    LaunchedEffect(registrationState) {
        when (registrationState) {
            is RegistrationState.Success -> {
                onRegistrationSuccess()
                viewModel.resetRegistrationState()
            }
            else -> {}
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Text(
            text = "Student Registration",
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(bottom = 32.dp)
        )
        
        // Registration Form
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                // Student Number
                OutlinedTextField(
                    value = studentNumber,
                    onValueChange = { 
                        if (it.length <= 8 && it.all { char -> char.isDigit() }) {
                            studentNumber = it
                        }
                    },
                    label = { Text("Student Number") },
                    placeholder = { Text("12345678") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    singleLine = true,
                    supportingText = { Text("8 digits only") }
                )
                
                // Name
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("First Name") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    singleLine = true
                )
                
                // Surname
                OutlinedTextField(
                    value = surname,
                    onValueChange = { surname = it },
                    label = { Text("Surname") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    singleLine = true
                )
                
                // Faculty Dropdown
                ExposedDropdownMenuBox(
                    expanded = showFacultyDropdown,
                    onExpandedChange = { showFacultyDropdown = !showFacultyDropdown },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    OutlinedTextField(
                        value = selectedFaculty.displayName,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Faculty") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showFacultyDropdown) },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showFacultyDropdown,
                        onDismissRequest = { showFacultyDropdown = false }
                    ) {
                        Faculty.values().forEach { faculty ->
                            DropdownMenuItem(
                                text = { Text(faculty.displayName) },
                                onClick = {
                                    selectedFaculty = faculty
                                    showFacultyDropdown = false
                                }
                            )
                        }
                    }
                }
                
                // Course
                OutlinedTextField(
                    value = course,
                    onValueChange = { course = it },
                    label = { Text("Course") },
                    placeholder = { Text("e.g., Computer Science") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    singleLine = true
                )
                
                // Glasses Checkbox
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = hasGlasses,
                        onCheckedChange = { hasGlasses = it }
                    )
                    Text(
                        text = "I wear glasses",
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
                
                // Face Scan Info
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 24.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Face Scan Required",
                            fontWeight = FontWeight.SemiBold,
                            fontSize = 16.sp,
                            modifier = Modifier.padding(bottom = 8.dp)
                        )
                        Text(
                            text = "After filling this form, you'll be asked to:",
                            fontSize = 14.sp,
                            modifier = Modifier.padding(bottom = 4.dp)
                        )
                        Text(
                            text = "• Perform head movements for liveness detection",
                            fontSize = 12.sp,
                            modifier = Modifier.padding(bottom = 2.dp)
                        )
                        Text(
                            text = "• Scan face without accessories",
                            fontSize = 12.sp,
                            modifier = Modifier.padding(bottom = 2.dp)
                        )
                        if (hasGlasses) {
                            Text(
                                text = "• Scan face with glasses",
                                fontSize = 12.sp
                            )
                        }
                    }
                }
                
                // Register Button
                Button(
                    onClick = {
                        if (studentNumber.length == 8 && 
                            name.isNotBlank() && 
                            surname.isNotBlank() && 
                            course.isNotBlank()) {
                            // For now, we'll register without face data
                            // Face scanning will be added in the next task
                            viewModel.registerStudent(
                                studentNumber = studentNumber,
                                name = name.trim(),
                                surname = surname.trim(),
                                faculty = selectedFaculty.displayName,
                                course = course.trim(),
                                faceDataWithoutGlasses = "placeholder", // Will be replaced with actual face data
                                faceDataWithGlasses = if (hasGlasses) "placeholder" else "",
                                hasGlasses = hasGlasses
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(50.dp),
                    enabled = registrationState !is RegistrationState.Loading &&
                            studentNumber.length == 8 &&
                            name.isNotBlank() &&
                            surname.isNotBlank() &&
                            course.isNotBlank()
                ) {
                    if (registrationState is RegistrationState.Loading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    } else {
                        Text("Continue to Face Scan", fontSize = 16.sp)
                    }
                }
                
                // Error Message
                if (registrationState is RegistrationState.Error) {
                    Text(
                        text = (registrationState as RegistrationState.Error).message,
                        color = MaterialTheme.colorScheme.error,
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(top = 16.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Back Button
        TextButton(
            onClick = onNavigateBack
        ) {
            Text("Back to Login")
        }
        
        // Generated Credentials Info
        if (studentNumber.length == 8) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Your Login Credentials:",
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 14.sp,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    Text(
                        text = "Email: ${studentNumber}@dut4life.ac.za",
                        fontSize = 12.sp,
                        modifier = Modifier.padding(bottom = 4.dp)
                    )
                    Text(
                        text = "Password: \$\$Dut${studentNumber.take(6)}",
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}
