package com.example.facetrack.ui.screens

import android.Manifest
import android.graphics.Bitmap
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Face
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import com.example.facetrack.viewmodel.FaceScanViewModel
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun FaceScanScreen(
    studentData: StudentRegistrationData,
    onNavigateBack: () -> Unit,
    onScanComplete: (String, String) -> Unit, // faceDataWithoutGlasses, faceDataWithGlasses
    faceScanViewModel: FaceScanViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)

    val scanState by faceScanViewModel.scanState.collectAsStateWithLifecycle()
    
    var scanStep by remember { mutableStateOf(ScanStep.PERMISSION_CHECK) }
    var instructionText by remember { mutableStateOf("Checking camera permission...") }
    var faceDataWithoutGlasses by remember { mutableStateOf("") }
    var faceDataWithGlasses by remember { mutableStateOf("") }
    
    // Handle permission state
    LaunchedEffect(cameraPermissionState.status) {
        when {
            cameraPermissionState.status.isGranted -> {
                scanStep = ScanStep.LIVENESS_CHECK
                instructionText = "Look straight at the camera"
            }
            cameraPermissionState.status.shouldShowRationale -> {
                scanStep = ScanStep.PERMISSION_DENIED
                instructionText = "Camera permission is required for face scanning"
            }
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Face Scan Registration") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Student Info Card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Student Information",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                    Text("Name: ${studentData.name} ${studentData.surname}")
                    Text("Student Number: ${studentData.studentNumber}")
                    Text("Faculty: ${studentData.faculty}")
                    Text("Course: ${studentData.course}")
                }
            }
            
            when (scanStep) {
                ScanStep.PERMISSION_CHECK -> {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Checking camera permission...")
                }
                
                ScanStep.PERMISSION_DENIED -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Face,
                            contentDescription = null,
                            modifier = Modifier.size(80.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "Camera Permission Required",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )

                        Text(
                            text = "This app needs camera access to scan your face for attendance tracking.",
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(bottom = 24.dp)
                        )

                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 24.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = "Why we need camera access:",
                                    fontWeight = FontWeight.SemiBold,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                Text("• Scan your face for registration", fontSize = 14.sp)
                                Text("• Verify your identity for attendance", fontSize = 14.sp)
                                Text("• Ensure secure access to the system", fontSize = 14.sp)
                            }
                        }

                        Button(
                            onClick = { cameraPermissionState.launchPermissionRequest() },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Grant Camera Permission")
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        TextButton(
                            onClick = onNavigateBack
                        ) {
                            Text("Go Back")
                        }
                    }
                }
                
                ScanStep.LIVENESS_CHECK -> {
                    // Face detection overlay with visual feedback
                    Box(
                        modifier = Modifier.size(300.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        // Camera preview
                        CameraPreview(
                            modifier = Modifier
                                .size(300.dp)
                                .clip(CircleShape),
                            lifecycleOwner = lifecycleOwner,
                            onFrameAnalyzed = { bitmap ->
                                faceScanViewModel.processCameraFrame(bitmap)
                            }
                        )

                        // Face detection overlay
                        Box(
                            modifier = Modifier
                                .size(300.dp)
                                .clip(CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            // Face detection indicator
                            if (scanState.faceDetected) {
                                // Green circle when face is detected
                                Box(
                                    modifier = Modifier
                                        .size(280.dp)
                                        .clip(CircleShape)
                                        .background(
                                            MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                        )
                                )
                            } else {
                                // Red circle when no face detected
                                Box(
                                    modifier = Modifier
                                        .size(280.dp)
                                        .clip(CircleShape)
                                        .background(
                                            MaterialTheme.colorScheme.error.copy(alpha = 0.3f)
                                        )
                                )
                            }

                            // Center crosshair
                            Icon(
                                imageVector = Icons.Default.Face,
                                contentDescription = null,
                                modifier = Modifier.size(48.dp),
                                tint = if (scanState.faceDetected)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.error
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    Text(
                        text = scanState.currentInstruction,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    // Face detection status
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = if (scanState.faceDetected)
                                MaterialTheme.colorScheme.primaryContainer
                            else
                                MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Row(
                            modifier = Modifier.padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (scanState.faceDetected) Icons.Default.Face else Icons.Default.ArrowBack,
                                contentDescription = null,
                                tint = if (scanState.faceDetected)
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                else
                                    MaterialTheme.colorScheme.onErrorContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = if (scanState.faceDetected) "Face Detected ✓" else "No Face Detected",
                                color = if (scanState.faceDetected)
                                    MaterialTheme.colorScheme.onPrimaryContainer
                                else
                                    MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                    }

                    // Progress indicator
                    LinearProgressIndicator(
                        progress = scanState.livenessProgress / 100f,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                    )

                    Text(
                        text = "${scanState.livenessProgress}% Complete",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    // Liveness detection instructions
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 24.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Follow these steps:",
                                fontWeight = FontWeight.SemiBold,
                                modifier = Modifier.padding(bottom = 8.dp)
                            )
                            Text("1. Look straight at the camera", fontSize = 14.sp)
                            Text("2. Slowly turn your head left", fontSize = 14.sp)
                            Text("3. Return to center", fontSize = 14.sp)
                            Text("4. Slowly turn your head right", fontSize = 14.sp)
                            Text("5. Return to center", fontSize = 14.sp)
                        }
                    }
                    
                    // Auto-complete when scan is done
                    if (scanState.isComplete) {
                        LaunchedEffect(scanState.isComplete) {
                            val faceEmbedding = faceScanViewModel.getFaceEmbeddingAsString()
                            faceDataWithoutGlasses = faceEmbedding

                            if (studentData.hasGlasses) {
                                scanStep = ScanStep.GLASSES_SCAN
                                faceScanViewModel.resetScan()
                            } else {
                                onScanComplete(faceDataWithoutGlasses, "")
                            }
                        }
                    }

                    // Manual capture button (for testing)
                    Button(
                        onClick = {
                            val faceEmbedding = faceScanViewModel.getFaceEmbeddingAsString()
                            faceDataWithoutGlasses = faceEmbedding

                            if (studentData.hasGlasses) {
                                scanStep = ScanStep.GLASSES_SCAN
                                faceScanViewModel.resetScan()
                            } else {
                                onScanComplete(faceDataWithoutGlasses, "")
                            }
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = scanState.faceDetected
                    ) {
                        Icon(Icons.Default.Face, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Capture Face (Without Glasses)")
                    }
                }
                
                ScanStep.GLASSES_SCAN -> {
                    // Placeholder for camera preview - will implement actual camera later
                    Card(
                        modifier = Modifier
                            .size(300.dp)
                            .clip(CircleShape),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant
                        )
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Face,
                                    contentDescription = null,
                                    modifier = Modifier.size(64.dp),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Text(
                                    text = "Camera Preview",
                                    fontSize = 16.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                                Text(
                                    text = "(With Glasses)",
                                    fontSize = 12.sp,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    Text(
                        text = instructionText,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                    
                    Button(
                        onClick = {
                            // Simulate face capture with glasses
                            faceDataWithGlasses = "face_data_${studentData.studentNumber}_with_glasses"
                            onScanComplete(faceDataWithoutGlasses, faceDataWithGlasses)
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(Icons.Default.Face, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Capture Face (With Glasses)")
                    }
                }
            }
        }
    }
}

@Composable
private fun CameraPreview(
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner,
    onFrameAnalyzed: (Bitmap) -> Unit
) {
    val context = LocalContext.current

    AndroidView(
        factory = { ctx ->
            val previewView = PreviewView(ctx)
            val cameraProviderFuture = ProcessCameraProvider.getInstance(ctx)

            cameraProviderFuture.addListener({
                try {
                    val cameraProvider = cameraProviderFuture.get()
                    val preview = Preview.Builder().build()
                    val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

                    // Image analysis for face detection
                    val imageAnalysis = ImageAnalysis.Builder()
                        .setTargetResolution(android.util.Size(640, 480))
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .build()

                    imageAnalysis.setAnalyzer(
                        ContextCompat.getMainExecutor(ctx)
                    ) { imageProxy ->
                        // Convert ImageProxy to Bitmap and analyze
                        val bitmap = imageProxyToBitmap(imageProxy)
                        if (bitmap != null) {
                            println("FaceScan: Bitmap created: ${bitmap.width}x${bitmap.height}")
                            onFrameAnalyzed(bitmap)
                        } else {
                            println("FaceScan: Failed to create bitmap from ImageProxy")
                        }
                        imageProxy.close()
                    }

                    preview.setSurfaceProvider(previewView.surfaceProvider)

                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageAnalysis
                    )
                } catch (exc: Exception) {
                    exc.printStackTrace()
                }
            }, ContextCompat.getMainExecutor(ctx))

            previewView
        },
        modifier = modifier
    )
}

private fun imageProxyToBitmap(imageProxy: ImageProxy): Bitmap? {
    return try {
        val buffer = imageProxy.planes[0].buffer
        val pixelStride = imageProxy.planes[0].pixelStride
        val rowStride = imageProxy.planes[0].rowStride
        val rowPadding = rowStride - pixelStride * imageProxy.width

        val bitmap = Bitmap.createBitmap(
            imageProxy.width + rowPadding / pixelStride,
            imageProxy.height,
            Bitmap.Config.ARGB_8888
        )
        bitmap.copyPixelsFromBuffer(buffer)

        // Crop to actual image size if there's padding
        if (rowPadding == 0) {
            bitmap
        } else {
            Bitmap.createBitmap(bitmap, 0, 0, imageProxy.width, imageProxy.height)
        }
    } catch (e: Exception) {
        e.printStackTrace()
        // Fallback: create a simple test bitmap
        Bitmap.createBitmap(640, 480, Bitmap.Config.ARGB_8888)
    }
}

enum class ScanStep {
    PERMISSION_CHECK,
    PERMISSION_DENIED,
    LIVENESS_CHECK,
    GLASSES_SCAN
}

data class StudentRegistrationData(
    val studentNumber: String,
    val name: String,
    val surname: String,
    val password: String,
    val faculty: String,
    val course: String,
    val hasGlasses: Boolean
)
