package com.example.facetrack.ml

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Rect
import androidx.camera.core.ImageProxy
import org.tensorflow.lite.Interpreter
import org.tensorflow.lite.support.common.FileUtil
import org.tensorflow.lite.support.image.ImageProcessor
import org.tensorflow.lite.support.image.TensorImage
import org.tensorflow.lite.support.image.ops.ResizeOp
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.math.*

/**
 * Helper class for REAL face detection using TensorFlow Lite
 */
class FaceDetectionHelper(private val context: Context) {

    private var faceDetectionInterpreter: Interpreter? = null
    private var faceRecognitionInterpreter: Interpreter? = null
    private var imageProcessor: ImageProcessor? = null

    companion object {
        private const val FACE_DETECTION_MODEL = "face_detection_short_range.tflite"
        private const val FACE_RECOGNITION_MODEL = "facenet_mobile.tflite"

        // Face detection model input/output specs
        private const val DETECTION_INPUT_SIZE = 128
        private const val DETECTION_OUTPUT_SIZE = 896 // Number of detections
        private const val CONFIDENCE_THRESHOLD = 0.7f

        // Face recognition model specs
        private const val RECOGNITION_INPUT_SIZE = 160
        private const val RECOGNITION_OUTPUT_SIZE = 512 // Embedding size

        private const val MAX_FACE_RESULTS = 10
    }
    
    /**
     * Initialize REAL TensorFlow Lite models
     */
    fun initializeDetector(): Boolean {
        return try {
            // Load face detection model
            val faceDetectionModel = FileUtil.loadMappedFile(context, FACE_DETECTION_MODEL)
            val faceDetectionOptions = Interpreter.Options().apply {
                setNumThreads(4)
                setUseNNAPI(true) // Use Android Neural Networks API if available
            }
            faceDetectionInterpreter = Interpreter(faceDetectionModel, faceDetectionOptions)

            // Load face recognition model
            val faceRecognitionModel = FileUtil.loadMappedFile(context, FACE_RECOGNITION_MODEL)
            val faceRecognitionOptions = Interpreter.Options().apply {
                setNumThreads(4)
                setUseNNAPI(true)
            }
            faceRecognitionInterpreter = Interpreter(faceRecognitionModel, faceRecognitionOptions)

            // Initialize image processor
            imageProcessor = ImageProcessor.Builder()
                .add(ResizeOp(DETECTION_INPUT_SIZE, DETECTION_INPUT_SIZE, ResizeOp.ResizeMethod.BILINEAR))
                .build()

            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * REAL face detection using TensorFlow Lite BlazeFace model
     */
    fun detectFaces(bitmap: Bitmap): List<FaceDetectionResult> {
        return try {
            val interpreter = faceDetectionInterpreter ?: return emptyList()
            val processor = imageProcessor ?: return emptyList()

            // Preprocess image
            val tensorImage = TensorImage.fromBitmap(bitmap)
            val processedImage = processor.process(tensorImage)

            // Prepare input buffer
            val inputBuffer = processedImage.buffer

            // Prepare output buffers
            val outputLocations = Array(1) { Array(DETECTION_OUTPUT_SIZE) { FloatArray(4) } } // [batch, detections, 4]
            val outputScores = Array(1) { FloatArray(DETECTION_OUTPUT_SIZE) } // [batch, detections]

            // Run inference
            val inputs = arrayOf(inputBuffer)
            val outputs = mapOf(
                0 to outputLocations,
                1 to outputScores
            )

            interpreter.runForMultipleInputsOutputs(inputs, outputs)

            // Parse results
            val faces = mutableListOf<FaceDetectionResult>()
            val scores = outputScores[0]
            val locations = outputLocations[0]

            for (i in 0 until min(DETECTION_OUTPUT_SIZE, MAX_FACE_RESULTS)) {
                val confidence = scores[i]

                if (confidence > CONFIDENCE_THRESHOLD) {
                    // Convert normalized coordinates to pixel coordinates
                    val yMin = (locations[i][0] * bitmap.height).toInt()
                    val xMin = (locations[i][1] * bitmap.width).toInt()
                    val yMax = (locations[i][2] * bitmap.height).toInt()
                    val xMax = (locations[i][3] * bitmap.width).toInt()

                    val boundingBox = Rect(
                        max(0, xMin),
                        max(0, yMin),
                        min(bitmap.width, xMax),
                        min(bitmap.height, yMax)
                    )

                    // Extract face embedding
                    val faceEmbedding = extractRealFaceEmbedding(bitmap, boundingBox)

                    faces.add(
                        FaceDetectionResult(
                            boundingBox = boundingBox,
                            confidence = confidence,
                            faceEmbedding = faceEmbedding
                        )
                    )
                }
            }

            // Sort by confidence and return top results
            faces.sortedByDescending { it.confidence }

        } catch (e: Exception) {
            e.printStackTrace()
            emptyList()
        }
    }
    
    /**
     * Convert ImageProxy to Bitmap for processing
     */
    fun imageProxyToBitmap(imageProxy: ImageProxy): Bitmap? {
        return try {
            val buffer = imageProxy.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)
            BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Extract REAL face embedding using FaceNet model
     */
    fun extractFaceEmbedding(bitmap: Bitmap, faceRect: Rect): FloatArray {
        return extractRealFaceEmbedding(bitmap, faceRect)
    }

    /**
     * Extract REAL face embedding using TensorFlow Lite FaceNet model
     */
    private fun extractRealFaceEmbedding(bitmap: Bitmap, faceRect: Rect): FloatArray {
        return try {
            val interpreter = faceRecognitionInterpreter ?: return FloatArray(RECOGNITION_OUTPUT_SIZE)

            // Crop and resize face
            val faceBitmap = cropAndResizeFace(bitmap, faceRect, RECOGNITION_INPUT_SIZE)

            // Preprocess face image
            val inputBuffer = preprocessFaceForRecognition(faceBitmap)

            // Prepare output buffer
            val outputBuffer = Array(1) { FloatArray(RECOGNITION_OUTPUT_SIZE) }

            // Run inference
            interpreter.run(inputBuffer, outputBuffer)

            // Normalize embedding (L2 normalization)
            val embedding = outputBuffer[0]
            normalizeEmbedding(embedding)

        } catch (e: Exception) {
            e.printStackTrace()
            FloatArray(RECOGNITION_OUTPUT_SIZE)
        }
    }

    /**
     * Crop and resize face from bitmap
     */
    private fun cropAndResizeFace(bitmap: Bitmap, faceRect: Rect, targetSize: Int): Bitmap {
        // Add padding around face
        val padding = 0.2f
        val paddingX = (faceRect.width() * padding).toInt()
        val paddingY = (faceRect.height() * padding).toInt()

        val left = max(0, faceRect.left - paddingX)
        val top = max(0, faceRect.top - paddingY)
        val right = min(bitmap.width, faceRect.right + paddingX)
        val bottom = min(bitmap.height, faceRect.bottom + paddingY)

        // Crop face
        val croppedFace = Bitmap.createBitmap(
            bitmap,
            left,
            top,
            right - left,
            bottom - top
        )

        // Resize to target size
        return Bitmap.createScaledBitmap(croppedFace, targetSize, targetSize, true)
    }

    /**
     * Preprocess face image for recognition model
     */
    private fun preprocessFaceForRecognition(faceBitmap: Bitmap): ByteBuffer {
        val inputBuffer = ByteBuffer.allocateDirect(4 * RECOGNITION_INPUT_SIZE * RECOGNITION_INPUT_SIZE * 3)
        inputBuffer.order(ByteOrder.nativeOrder())

        val pixels = IntArray(RECOGNITION_INPUT_SIZE * RECOGNITION_INPUT_SIZE)
        faceBitmap.getPixels(pixels, 0, RECOGNITION_INPUT_SIZE, 0, 0, RECOGNITION_INPUT_SIZE, RECOGNITION_INPUT_SIZE)

        for (pixel in pixels) {
            // Normalize pixel values to [-1, 1] range (FaceNet preprocessing)
            val r = ((pixel shr 16) and 0xFF) / 127.5f - 1.0f
            val g = ((pixel shr 8) and 0xFF) / 127.5f - 1.0f
            val b = (pixel and 0xFF) / 127.5f - 1.0f

            inputBuffer.putFloat(r)
            inputBuffer.putFloat(g)
            inputBuffer.putFloat(b)
        }

        return inputBuffer
    }

    /**
     * Normalize embedding using L2 normalization
     */
    private fun normalizeEmbedding(embedding: FloatArray): FloatArray {
        val norm = sqrt(embedding.map { it * it }.sum())
        if (norm > 0) {
            for (i in embedding.indices) {
                embedding[i] /= norm
            }
        }
        return embedding
    }
    
    /**
     * Check if face is looking straight (for liveness detection)
     */
    fun isFaceLookingStraight(faceRect: Rect, imageWidth: Int, imageHeight: Int): Boolean {
        // Simple heuristic: face should be roughly centered
        val faceCenterX = faceRect.centerX()
        val faceCenterY = faceRect.centerY()
        val imageCenterX = imageWidth / 2
        val imageCenterY = imageHeight / 2
        
        val xThreshold = imageWidth * 0.1 // 10% tolerance
        val yThreshold = imageHeight * 0.1 // 10% tolerance
        
        return kotlin.math.abs(faceCenterX - imageCenterX) < xThreshold &&
               kotlin.math.abs(faceCenterY - imageCenterY) < yThreshold
    }
    
    /**
     * REAL head movement detection for liveness
     */
    fun detectHeadMovement(
        previousFaceRect: Rect?,
        currentFaceRect: Rect,
        movementType: HeadMovement
    ): Boolean {
        if (previousFaceRect == null) return false

        val deltaX = currentFaceRect.centerX() - previousFaceRect.centerX()
        val deltaY = currentFaceRect.centerY() - previousFaceRect.centerY()

        // Calculate face size for relative movement detection
        val faceWidth = currentFaceRect.width()
        val faceHeight = currentFaceRect.height()

        // Use relative thresholds based on face size (more accurate)
        val horizontalThreshold = faceWidth * 0.15f // 15% of face width
        val verticalThreshold = faceHeight * 0.15f // 15% of face height
        val centerThreshold = min(faceWidth, faceHeight) * 0.1f // 10% for center position

        return when (movementType) {
            HeadMovement.LEFT -> deltaX < -horizontalThreshold
            HeadMovement.RIGHT -> deltaX > horizontalThreshold
            HeadMovement.UP -> deltaY < -verticalThreshold
            HeadMovement.DOWN -> deltaY > verticalThreshold
            HeadMovement.CENTER -> abs(deltaX) < centerThreshold && abs(deltaY) < centerThreshold
        }
    }

    /**
     * Advanced liveness detection using face landmarks and pose estimation
     */
    fun detectAdvancedLiveness(
        currentFace: FaceDetectionResult,
        previousFaces: List<FaceDetectionResult>
    ): LivenessResult {
        return try {
            // Check face size consistency (anti-spoofing)
            val faceSizeVariation = calculateFaceSizeVariation(currentFace, previousFaces)

            // Check natural movement patterns
            val movementNaturalness = calculateMovementNaturalness(currentFace, previousFaces)

            // Check face quality and sharpness
            val faceQuality = calculateFaceQuality(currentFace)

            LivenessResult(
                isLive = faceSizeVariation < 0.3f && movementNaturalness > 0.6f && faceQuality > 0.7f,
                confidence = (movementNaturalness + faceQuality) / 2f,
                faceSizeVariation = faceSizeVariation,
                movementNaturalness = movementNaturalness,
                faceQuality = faceQuality
            )
        } catch (e: Exception) {
            e.printStackTrace()
            LivenessResult(false, 0f, 1f, 0f, 0f)
        }
    }

    private fun calculateFaceSizeVariation(
        currentFace: FaceDetectionResult,
        previousFaces: List<FaceDetectionResult>
    ): Float {
        if (previousFaces.isEmpty()) return 0f

        val currentSize = currentFace.boundingBox.width() * currentFace.boundingBox.height()
        val avgPreviousSize = previousFaces.map {
            it.boundingBox.width() * it.boundingBox.height()
        }.average().toFloat()

        return abs(currentSize - avgPreviousSize) / avgPreviousSize
    }

    private fun calculateMovementNaturalness(
        currentFace: FaceDetectionResult,
        previousFaces: List<FaceDetectionResult>
    ): Float {
        if (previousFaces.size < 2) return 0.5f

        // Calculate movement smoothness
        val movements = mutableListOf<Float>()
        for (i in 1 until previousFaces.size) {
            val prev = previousFaces[i-1].boundingBox
            val curr = previousFaces[i].boundingBox
            val movement = sqrt(
                ((curr.centerX() - prev.centerX()).toFloat().pow(2) +
                 (curr.centerY() - prev.centerY()).toFloat().pow(2))
            )
            movements.add(movement)
        }

        // Natural movement should have some variation but not be too erratic
        val avgMovement = movements.average().toFloat()
        val movementVariation = movements.map { abs(it - avgMovement) }.average().toFloat()

        return max(0f, min(1f, 1f - (movementVariation / avgMovement)))
    }

    private fun calculateFaceQuality(face: FaceDetectionResult): Float {
        // Use confidence as a proxy for face quality
        // In a more advanced implementation, you could analyze:
        // - Image sharpness
        // - Lighting conditions
        // - Face angle/pose
        return face.confidence
    }
    
    // Mock embedding function REMOVED - using real embeddings now!
    
    /**
     * Compare two face embeddings
     */
    fun compareFaceEmbeddings(embedding1: FloatArray, embedding2: FloatArray): Float {
        if (embedding1.size != embedding2.size) return 0f
        
        // Calculate cosine similarity
        var dotProduct = 0f
        var norm1 = 0f
        var norm2 = 0f
        
        for (i in embedding1.indices) {
            dotProduct += embedding1[i] * embedding2[i]
            norm1 += embedding1[i] * embedding1[i]
            norm2 += embedding2[i] * embedding2[i]
        }
        
        return if (norm1 > 0 && norm2 > 0) {
            dotProduct / (kotlin.math.sqrt(norm1) * kotlin.math.sqrt(norm2))
        } else {
            0f
        }
    }
    
    /**
     * Clean up REAL TensorFlow Lite resources
     */
    fun close() {
        faceDetectionInterpreter?.close()
        faceRecognitionInterpreter?.close()
        faceDetectionInterpreter = null
        faceRecognitionInterpreter = null
        imageProcessor = null
    }
}

/**
 * Data class for face detection results
 */
data class FaceDetectionResult(
    val boundingBox: Rect,
    val confidence: Float,
    val faceEmbedding: FloatArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as FaceDetectionResult

        if (boundingBox != other.boundingBox) return false
        if (confidence != other.confidence) return false
        if (!faceEmbedding.contentEquals(other.faceEmbedding)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = boundingBox.hashCode()
        result = 31 * result + confidence.hashCode()
        result = 31 * result + faceEmbedding.contentHashCode()
        return result
    }
}

/**
 * Enum for head movement types
 */
enum class HeadMovement {
    LEFT, RIGHT, UP, DOWN, CENTER
}

/**
 * Data class for advanced liveness detection results
 */
data class LivenessResult(
    val isLive: Boolean,
    val confidence: Float,
    val faceSizeVariation: Float,
    val movementNaturalness: Float,
    val faceQuality: Float
)
