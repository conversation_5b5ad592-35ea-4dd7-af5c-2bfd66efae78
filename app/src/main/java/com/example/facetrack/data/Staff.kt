package com.example.facetrack.data

import com.google.firebase.firestore.DocumentId

/**
 * Data class representing a Staff member (Lecturer) in the system
 */
data class Staff(
    @DocumentId
    val staffId: String = "", // Primary key - custom format
    val name: String = "",
    val surname: String = "",
    val email: String = "", // format: <EMAIL>
    val password: String = "", // Custom password set by admin
    val department: String = "",
    val faculty: String = "",
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this(
        staffId = "",
        name = "",
        surname = "",
        email = "",
        password = "",
        department = "",
        faculty = "",
        isActive = true,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}

/**
 * Enum for different departments
 */
enum class Department(val displayName: String, val faculty: Faculty) {
    // Engineering departments
    CIVIL_ENGINEERING("Civil Engineering", Faculty.ENGINEERING),
    ELECTRICAL_ENGINEERING("Electrical Engineering", Faculty.ENGINEERING),
    MECHANICAL_ENGINEERING("Mechanical Engineering", Faculty.ENGINEERING),
    CHEMICAL_ENGINEERING("Chemical Engineering", Faculty.ENGINEERING),
    
    // Applied Sciences departments
    INFORMATION_TECHNOLOGY("Information Technology", Faculty.APPLIED_SCIENCES),
    COMPUTER_SCIENCE("Computer Science", Faculty.APPLIED_SCIENCES),
    MATHEMATICS("Mathematics", Faculty.APPLIED_SCIENCES),
    PHYSICS("Physics", Faculty.APPLIED_SCIENCES),
    
    // Other departments can be added as needed
    ACCOUNTING("Accounting", Faculty.ACCOUNTING_INFORMATICS),
    BUSINESS_ADMINISTRATION("Business Administration", Faculty.MANAGEMENT_SCIENCES)
}
