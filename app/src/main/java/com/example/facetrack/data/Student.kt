package com.example.facetrack.data

import com.google.firebase.firestore.DocumentId

/**
 * Data class representing a Student in the system
 */
data class Student(
    @DocumentId
    val studentNumber: String = "", // Primary key - 8 digits
    val name: String = "",
    val surname: String = "",
    val email: String = "", // format: <EMAIL>
    val password: String = "", // format: $$Dut + first 6 digits of ID
    val faculty: String = "",
    val course: String = "",
    val faceDataWithoutGlasses: String = "", // Base64 encoded face embedding
    val faceDataWithGlasses: String = "", // Base64 encoded face embedding (optional)
    val hasGlasses: Boolean = false,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this(
        studentNumber = "",
        name = "",
        surname = "",
        email = "",
        password = "",
        faculty = "",
        course = "",
        faceDataWithoutGlasses = "",
        faceDataWithGlasses = "",
        hasGlasses = false,
        isActive = true,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}

/**
 * Enum for different faculties
 */
enum class Faculty(val displayName: String) {
    ENGINEERING("Faculty of Engineering"),
    APPLIED_SCIENCES("Faculty of Applied Sciences"),
    ACCOUNTING_INFORMATICS("Faculty of Accounting and Informatics"),
    ARTS_DESIGN("Faculty of Arts and Design"),
    HEALTH_SCIENCES("Faculty of Health Sciences"),
    MANAGEMENT_SCIENCES("Faculty of Management Sciences")
}
