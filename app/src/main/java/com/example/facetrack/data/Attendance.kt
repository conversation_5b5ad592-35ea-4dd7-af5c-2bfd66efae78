package com.example.facetrack.data

import com.google.firebase.firestore.DocumentId

/**
 * Data class representing an Attendance record
 */
data class Attendance(
    @DocumentId
    val attendanceId: String = "", // Auto-generated document ID
    val studentNumber: String = "",
    val studentName: String = "",
    val studentSurname: String = "",
    val courseCode: String = "",
    val courseName: String = "",
    val lecturerStaffId: String = "",
    val lecturerName: String = "",
    val sessionType: SessionType = SessionType.LECTURE,
    val sessionDate: String = "", // Format: YYYY-MM-DD
    val sessionTime: String = "", // Format: HH:MM
    val checkInTime: Long = 0L, // Timestamp when student was marked present
    val isPresent: Boolean = false,
    val confidenceScore: Float = 0.0f, // Face recognition confidence (0.0 - 1.0)
    val location: String = "", // Classroom/venue
    val createdAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this(
        attendanceId = "",
        studentNumber = "",
        studentName = "",
        studentSurname = "",
        courseCode = "",
        courseName = "",
        lecturerStaffId = "",
        lecturerName = "",
        sessionType = SessionType.LECTURE,
        sessionDate = "",
        sessionTime = "",
        checkInTime = 0L,
        isPresent = false,
        confidenceScore = 0.0f,
        location = "",
        createdAt = System.currentTimeMillis()
    )
}

/**
 * Enum for different session types
 */
enum class SessionType(val displayName: String) {
    LECTURE("Lecture"),
    TUTORIAL("Tutorial"),
    PRACTICAL("Practical"),
    LAB("Laboratory"),
    SEMINAR("Seminar")
}

/**
 * Data class for attendance statistics
 */
data class AttendanceStats(
    val studentNumber: String = "",
    val courseCode: String = "",
    val totalSessions: Int = 0,
    val attendedSessions: Int = 0,
    val attendancePercentage: Float = 0.0f,
    val attendanceMark: Float = 0.0f, // Mark allocated by lecturer
    val lastUpdated: Long = System.currentTimeMillis()
) {
    constructor() : this(
        studentNumber = "",
        courseCode = "",
        totalSessions = 0,
        attendedSessions = 0,
        attendancePercentage = 0.0f,
        attendanceMark = 0.0f,
        lastUpdated = System.currentTimeMillis()
    )
}
