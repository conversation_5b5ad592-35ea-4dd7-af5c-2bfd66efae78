package com.example.facetrack.data

import com.google.firebase.firestore.DocumentId

/**
 * Data class representing a Course
 */
data class Course(
    @DocumentId
    val courseCode: String = "", // Primary key - e.g., "CS101", "ENG201"
    val courseName: String = "",
    val faculty: String = "",
    val department: String = "",
    val lecturerStaffId: String = "",
    val lecturerName: String = "",
    val credits: Int = 0,
    val semester: Int = 1, // 1 or 2
    val year: Int = 2024,
    val enrolledStudents: List<String> = emptyList(), // List of student numbers
    val sessionSchedule: List<SessionSchedule> = emptyList(),
    val attendanceWeight: Float = 10.0f, // Percentage weight for attendance in final mark
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) {
    // No-argument constructor for Firestore
    constructor() : this(
        courseCode = "",
        courseName = "",
        faculty = "",
        department = "",
        lecturerStaffId = "",
        lecturerName = "",
        credits = 0,
        semester = 1,
        year = 2024,
        enrolledStudents = emptyList(),
        sessionSchedule = emptyList(),
        attendanceWeight = 10.0f,
        isActive = true,
        createdAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis()
    )
}

/**
 * Data class representing a scheduled session for a course
 */
data class SessionSchedule(
    val sessionType: SessionType = SessionType.LECTURE,
    val dayOfWeek: String = "", // "MONDAY", "TUESDAY", etc.
    val startTime: String = "", // "08:00"
    val endTime: String = "", // "09:00"
    val venue: String = "", // Classroom/venue
    val isRecurring: Boolean = true
) {
    constructor() : this(
        sessionType = SessionType.LECTURE,
        dayOfWeek = "",
        startTime = "",
        endTime = "",
        venue = "",
        isRecurring = true
    )
}
